// ignore_for_file: avoid_print
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mytank/utilities/auth_manager.dart';
import 'package:mytank/providers/auth_provider.dart';

void main() {
  group('Authentication Flow Integration Tests', () {
    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('should restore authentication state on app restart simulation', () async {
      print('🔄 === SIMULATING COMPLETE AUTH FLOW ===');

      // === STEP 1: Simulate successful login ===
      print('\n📱 STEP 1: Simulating successful login...');

      const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token_12345';
      const testUserName = 'John Doe';
      const testUserId = 'user_123';

      // Manually save auth state (simulating what happens after successful login)
      await AuthManager.saveAuthState(
        token: testToken,
        userName: testUserName,
        userId: testUserId,
      );

      print('✅ Auth state saved after login');

      // Verify state was saved
      final savedState = await AuthManager.getAuthState();
      expect(savedState.isValid, isTrue);
      expect(savedState.token, equals(testToken));
      expect(savedState.userName, equals(testUserName));
      expect(savedState.userId, equals(testUserId));

      print('✅ Verified auth state is saved correctly');

      // === STEP 2: Simulate app restart ===
      print('\n🔄 STEP 2: Simulating app restart...');

      // Create a new AuthProvider instance (simulating app restart)
      final newAuthProvider = AuthProvider();

      // This should be false initially (before initialization)
      expect(newAuthProvider.isAuthenticated, isFalse);
      print('✅ New AuthProvider starts unauthenticated (expected)');

      // === STEP 3: Initialize AuthProvider (what happens in splash screen) ===
      print(
        '\n🔍 STEP 3: Initializing AuthProvider (splash screen simulation)...',
      );

      // Note: We'll skip token validation for this test since we don't have a real server
      // In real app, this would validate the token with the server

      // Load the saved auth state
      final authState = await AuthManager.getAuthState();
      print('📱 Loaded auth state: $authState');

      expect(authState.isValid, isTrue);
      expect(authState.token, equals(testToken));
      expect(authState.userName, equals(testUserName));
      expect(authState.userId, equals(testUserId));

      // Since we can't access private fields directly, we'll test the AuthManager directly
      // This simulates what the AuthProvider.initialize() method should do

      // Verify that AuthManager can provide the correct state
      expect(authState.token, equals(testToken));
      expect(authState.userName, equals(testUserName));
      expect(authState.userId, equals(testUserId));
      expect(authState.isValid, isTrue);
      expect(authState.isLoggedIn, isTrue);

      print('✅ AuthManager can provide correct authentication state');

      // === STEP 4: Verify persistence across multiple restarts ===
      print('\n🔄 STEP 4: Testing multiple app restarts...');

      for (int i = 1; i <= 3; i++) {
        print('  Restart #$i...');

        final restartAuthState = await AuthManager.getAuthState();

        expect(restartAuthState.isValid, isTrue);
        expect(restartAuthState.token, equals(testToken));

        print('  ✅ Restart #$i: Auth state still valid');
      }

      // === STEP 5: Test logout and verify cleanup ===
      print('\n🚪 STEP 5: Testing logout...');

      await AuthManager.clearAuthState();

      final clearedState = await AuthManager.getAuthState();
      expect(clearedState.isValid, isFalse);
      expect(clearedState.token, isNull);

      print('✅ Logout successful, all auth data cleared');

      // === STEP 6: Verify no auth state after logout ===
      print('\n🔍 STEP 6: Verifying clean state after logout...');

      final postLogoutState = await AuthManager.getAuthState();
      expect(postLogoutState.isValid, isFalse);

      print('✅ No authentication state found after logout (expected)');

      print('\n🎉 === AUTHENTICATION FLOW TEST COMPLETED SUCCESSFULLY ===');
    });

    test('should handle invalid/corrupted auth state gracefully', () async {
      print('🔄 Testing invalid auth state handling...');

      // Save invalid auth state (token without login flag)
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', 'invalid_token');
      // Don't set is_logged_in flag

      final authState = await AuthManager.getAuthState();
      expect(
        authState.isValid,
        isFalse,
      ); // Should be invalid without login flag

      print('✅ Invalid auth state handled correctly');
    });

    test('should handle missing user info gracefully', () async {
      print('🔄 Testing auth state with missing user info...');

      // Save auth state with only token
      await AuthManager.saveAuthState(token: 'token_only');

      final authState = await AuthManager.getAuthState();
      expect(authState.isValid, isTrue); // Should still be valid
      expect(authState.hasUserInfo, isFalse); // But no user info

      print('✅ Auth state with missing user info handled correctly');
    });
  });
}
