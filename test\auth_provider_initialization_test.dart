// ignore_for_file: avoid_print
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mytank/utilities/auth_manager.dart';
import 'package:mytank/providers/auth_provider.dart';

void main() {
  group('AuthProvider Initialization Tests', () {
    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('should restore authentication state during initialization', () async {
      // === STEP 1: Save authentication state (simulating successful login) ===
      const testToken = 'test_jwt_token_12345';
      const testUserName = 'Test User';
      const testUserId = 'test_user_id';

      await AuthManager.saveAuthState(
        token: testToken,
        userName: testUserName,
        userId: testUserId,
      );

      // Verify state was saved
      final savedState = await AuthManager.getAuthState();
      expect(savedState.isValid, isTrue);
      expect(savedState.token, equals(testToken));
      expect(savedState.userName, equals(testUserName));
      expect(savedState.userId, equals(testUserId));

      // === STEP 2: Create new AuthProvider and initialize (simulating app restart) ===
      final authProvider = AuthProvider();

      // Before initialization, should not be authenticated
      expect(authProvider.isAuthenticated, isFalse);
      expect(authProvider.accessToken, isNull);
      expect(authProvider.userName, isNull);
      expect(authProvider.userId, isNull);

      // Initialize the provider (this is what happens in splash screen)
      await authProvider.initialize();

      // After initialization, should be authenticated (with token validation disabled)
      expect(authProvider.isAuthenticated, isTrue);
      expect(authProvider.accessToken, equals(testToken));
      expect(authProvider.userName, equals(testUserName));
      expect(authProvider.userId, equals(testUserId));

      print('✅ AuthProvider successfully restored authentication state');
      print('🔑 Token: ${authProvider.accessToken}');
      print('👤 User: ${authProvider.userName}');
      print('🆔 User ID: ${authProvider.userId}');
    });

    test(
      'should handle empty authentication state during initialization',
      () async {
        // No saved authentication state
        final authProvider = AuthProvider();

        // Initialize with empty state
        await authProvider.initialize();

        // Should remain unauthenticated
        expect(authProvider.isAuthenticated, isFalse);
        expect(authProvider.accessToken, isNull);
        expect(authProvider.userName, isNull);
        expect(authProvider.userId, isNull);

        print('✅ AuthProvider correctly handled empty authentication state');
      },
    );

    test('should handle logout and clear state', () async {
      // === STEP 1: Set up authenticated state ===
      const testToken = 'logout_test_token';
      const testUserName = 'Logout Test User';

      await AuthManager.saveAuthState(token: testToken, userName: testUserName);

      final authProvider = AuthProvider();
      await authProvider.initialize();

      // Should be authenticated
      expect(authProvider.isAuthenticated, isTrue);
      expect(authProvider.accessToken, equals(testToken));
      expect(authProvider.userName, equals(testUserName));

      // === STEP 2: Logout ===
      await authProvider.logout();

      // Should be logged out
      expect(authProvider.isAuthenticated, isFalse);
      expect(authProvider.accessToken, isNull);
      expect(authProvider.userName, isNull);
      expect(authProvider.userId, isNull);

      // Verify persistent state is also cleared
      final clearedState = await AuthManager.getAuthState();
      expect(clearedState.isValid, isFalse);
      expect(clearedState.token, isNull);

      print('✅ Logout successfully cleared all authentication state');
    });

    test('should persist state across multiple initializations', () async {
      // === STEP 1: Save initial state ===
      const testToken = 'persistence_test_token';
      const testUserName = 'Persistence Test User';
      const testUserId = 'persistence_test_id';

      await AuthManager.saveAuthState(
        token: testToken,
        userName: testUserName,
        userId: testUserId,
      );

      // === STEP 2: Test multiple initializations ===
      for (int i = 1; i <= 5; i++) {
        final authProvider = AuthProvider();
        await authProvider.initialize();

        expect(authProvider.isAuthenticated, isTrue);
        expect(authProvider.accessToken, equals(testToken));
        expect(authProvider.userName, equals(testUserName));
        expect(authProvider.userId, equals(testUserId));

        print('✅ Initialization #$i: Authentication state restored correctly');
      }

      print('✅ Authentication state persisted across multiple initializations');
    });

    test('should update user info and persist changes', () async {
      // === STEP 1: Initialize with basic state ===
      const initialToken = 'update_test_token';

      await AuthManager.saveAuthState(token: initialToken);

      final authProvider = AuthProvider();
      await authProvider.initialize();

      expect(authProvider.isAuthenticated, isTrue);
      expect(authProvider.userName, isNull); // No user info initially

      // === STEP 2: Update user info ===
      const newUserName = 'Updated User Name';
      const newUserId = 'updated_user_id';

      await authProvider.updateUserInfo(newUserName);
      await authProvider.updateUserId(newUserId);

      // Check local state
      expect(authProvider.userName, equals(newUserName));
      expect(authProvider.userId, equals(newUserId));

      // === STEP 3: Verify persistence ===
      final newAuthProvider = AuthProvider();
      await newAuthProvider.initialize();

      expect(newAuthProvider.userName, equals(newUserName));
      expect(newAuthProvider.userId, equals(newUserId));

      print('✅ User info updates persisted correctly');
    });
  });
}
