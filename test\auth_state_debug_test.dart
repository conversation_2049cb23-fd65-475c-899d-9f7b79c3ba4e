// ignore_for_file: avoid_print
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:mytank/utilities/auth_manager.dart';

void main() {
  group('Auth State Debug Tests', () {
    setUp(() async {
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    test('should simulate login and verify state persistence', () async {
      // Simulate a login scenario
      const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test_token';
      const testUserName = 'Test User';
      const testUserId = 'test_user_123';

      print('🔄 Simulating login...');

      // Save auth state (simulating successful login)
      await AuthManager.saveAuthState(
        token: testToken,
        userName: testUserName,
        userId: testUserId,
      );

      print('✅ Auth state saved');

      // Verify state was saved correctly
      final authState = await AuthManager.getAuthState();
      print('📱 Retrieved auth state: $authState');

      expect(authState.token, equals(testToken));
      expect(authState.userName, equals(testUserName));
      expect(authState.userId, equals(testUserId));
      expect(authState.isLoggedIn, isTrue);
      expect(authState.isValid, isTrue);

      // Check individual getters
      final token = await AuthManager.getToken();
      final userName = await AuthManager.getUserName();
      final userId = await AuthManager.getUserId();
      final isLoggedIn = await AuthManager.isLoggedIn();
      final hasValidState = await AuthManager.hasValidAuthState();

      print('🔍 Individual checks:');
      print('  Token: ${token?.substring(0, 20)}...');
      print('  User Name: $userName');
      print('  User ID: $userId');
      print('  Is Logged In: $isLoggedIn');
      print('  Has Valid State: $hasValidState');

      expect(token, equals(testToken));
      expect(userName, equals(testUserName));
      expect(userId, equals(testUserId));
      expect(isLoggedIn, isTrue);
      expect(hasValidState, isTrue);
    });

    test('should check what happens with empty/invalid states', () async {
      print('🔄 Testing empty state...');

      final authState = await AuthManager.getAuthState();
      print('📱 Empty auth state: $authState');

      expect(authState.isValid, isFalse);
      expect(authState.isLoggedIn, isFalse);
      expect(await AuthManager.hasValidAuthState(), isFalse);
    });

    test('should test state with only token (no user info)', () async {
      print('🔄 Testing token-only state...');

      const testToken = 'token_without_user_info';

      await AuthManager.saveAuthState(token: testToken);

      final authState = await AuthManager.getAuthState();
      print('📱 Token-only auth state: $authState');

      expect(authState.token, equals(testToken));
      expect(authState.isLoggedIn, isTrue);
      expect(
        authState.isValid,
        isTrue,
      ); // Should be valid even without user info
      expect(authState.hasUserInfo, isFalse);
    });

    test('should verify SharedPreferences keys directly', () async {
      print('🔄 Testing SharedPreferences keys directly...');

      const testToken = 'direct_test_token';
      const testUserName = 'Direct Test User';

      await AuthManager.saveAuthState(token: testToken, userName: testUserName);

      // Check SharedPreferences directly
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      print('📱 SharedPreferences keys: $keys');
      print('📱 auth_token: ${prefs.getString('auth_token')}');
      print('📱 user_name: ${prefs.getString('user_name')}');
      print('📱 user_id: ${prefs.getString('user_id')}');
      print('📱 is_logged_in: ${prefs.getBool('is_logged_in')}');
      print('📱 login_timestamp: ${prefs.getInt('login_timestamp')}');

      expect(prefs.getString('auth_token'), equals(testToken));
      expect(prefs.getString('user_name'), equals(testUserName));
      expect(prefs.getBool('is_logged_in'), isTrue);
      expect(prefs.getInt('login_timestamp'), isNotNull);
    });
  });
}
